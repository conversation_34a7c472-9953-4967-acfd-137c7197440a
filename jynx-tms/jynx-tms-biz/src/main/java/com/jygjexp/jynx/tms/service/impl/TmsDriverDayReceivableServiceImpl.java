package com.jygjexp.jynx.tms.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.jygjexp.jynx.common.core.util.R;
import com.jygjexp.jynx.tms.dto.TmsDriverPieceCalculationResultDto;
import com.jygjexp.jynx.tms.entity.*;

import com.jygjexp.jynx.tms.mapper.TmsDriverDayReceivableMapper;
import com.jygjexp.jynx.tms.service.*;
import com.jygjexp.jynx.tms.vo.TmsDriverDayReceivablePageVo;
import com.jygjexp.jynx.tms.vo.TmsDriverPieceCalculationRequestVo;
import com.jygjexp.jynx.tms.vo.TmsDriverPieceCalculationResponseVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
/**
 * 司机每日应收费用表
 *
 * <AUTHOR>
 * @date 2025-07-22 17:00:53
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TmsDriverDayReceivableServiceImpl extends ServiceImpl<TmsDriverDayReceivableMapper, TmsDriverDayReceivableEntity> implements TmsDriverDayReceivableService {

    private final TmsDriverDayReceivableMapper driverDayReceivableMapper;
    private final TmsDriverBillingTemplateService templateService;
    private final TmsDriverBillingPieceService pieceService;
    private final TmsDriverBillingPieceFreightService freightService;

    private final TmsLmdDriverService lmdDriverService;
    private final TmsDriverSignService driverSignService;
    private final TmsCustomerOrderService customerOrderService;

    /**
     * 计费模式常量
     */
    private static final Integer BILLING_MODE_PIECE = 2;  // 计件模式


    // 分页查询
    @Override
    public Page<TmsDriverDayReceivablePageVo> search(Page page, TmsDriverDayReceivablePageVo vo) {
        MPJLambdaWrapper<TmsDriverDayReceivableEntity> wrapper = new MPJLambdaWrapper<>();
        wrapper.selectAll(TmsDriverDayReceivableEntity.class)
                .selectAs(TmsLmdDriverEntity::getDriverName, TmsDriverDayReceivablePageVo.Fields.driverName)
                .leftJoin(TmsLmdDriverEntity.class, TmsLmdDriverEntity::getDriverId, TmsDriverDayReceivableEntity::getDriverId)
                .like(StrUtil.isNotBlank(vo.getDriverName()), TmsLmdDriverEntity::getDriverName, vo.getDriverName())
                .eq(ObjectUtil.isNotNull(vo.getVehicleType()), TmsDriverDayReceivableEntity::getVehicleType, vo.getVehicleType())
                .between(ObjectUtil.isNotNull(vo.getStartTime()) && ObjectUtil.isNotNull(vo.getEndTime()),
                        TmsDriverDayReceivableEntity::getAttendanceDate, vo.getStartTime(), vo.getEndTime())
                .orderByDesc(TmsDriverDayReceivableEntity::getCreateTime);
        return driverDayReceivableMapper.selectJoinPage(page,TmsDriverDayReceivablePageVo.class,wrapper);
    }

    /**
     * 计算司机计件模式下的每日应收费用
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R calculateDriverPieceFee(TmsDriverPieceCalculationRequestVo requestVo) {
        try {
            log.info("开始计算司机计件费用，计算日期：{}", requestVo.getCalculationDate());

            // 参数校验
            if (requestVo.getCalculationDate() == null) {
                return R.failed("计算日期不能为空");
            }

            // 初始化响应对象
            TmsDriverPieceCalculationResponseVo responseVo = new TmsDriverPieceCalculationResponseVo();
            BeanUtils.copyProperties(requestVo, responseVo);

            // 获取需要计算的司机列表
            List<Long> targetDriverIds = getTargetDriverIds(requestVo);
            if (CollUtil.isEmpty(targetDriverIds)) {
                log.warn("未找到需要计算的司机");
                return R.ok(responseVo);
            }

            log.info("找到{}个需要计算的司机", targetDriverIds.size());

            // 逐个计算司机费用
            for (Long driverId : targetDriverIds) {
                TmsDriverPieceCalculationResultDto result = calculateSingleDriverFee(driverId, requestVo);
                responseVo.getDriverResults().add(result);

                // 统计结果
                switch (result.getStatus()) {
                    case "SUCCESS":
                        responseVo.setSuccessCount(responseVo.getSuccessCount() + 1);
                        break;
                    case "FAILED":
                        responseVo.setFailedCount(responseVo.getFailedCount() + 1);
                        if (StrUtil.isNotBlank(result.getErrorMessage())) {
                            responseVo.getErrorMessages().add(result.getDriverName() + ": " + result.getErrorMessage());
                        }
                        break;
                    case "SKIPPED":
                        responseVo.setSkippedCount(responseVo.getSkippedCount() + 1);
                        break;
                }
            }

            log.info("司机计件费用计算完成，成功：{}，失败：{}，跳过：{}", responseVo.getSuccessCount(), responseVo.getFailedCount(), responseVo.getSkippedCount());

            return R.ok(responseVo);

        } catch (Exception e) {
            log.error("计算司机计件费用异常", e);
            throw new RuntimeException("计算司机计件费用失败");
        }
    }

    /**
     * 获取需要计算的司机ID列表
     */
    private List<Long> getTargetDriverIds(TmsDriverPieceCalculationRequestVo requestVo) {
        List<Long> targetDriverIds = new ArrayList<>();

        if (requestVo.getDriverIds() != null && requestVo.getDriverIds().length > 0) {
            // 如果指定了司机ID，直接使用
            targetDriverIds.addAll(Arrays.asList(requestVo.getDriverIds()));
        } else {
            // 否则查询所有计件模式的司机
            LambdaQueryWrapper<TmsDriverBillingTemplateEntity> templateWrapper = new LambdaQueryWrapper<>();
            templateWrapper.eq(TmsDriverBillingTemplateEntity::getBillingMode, BILLING_MODE_PIECE)
                    .eq(TmsDriverBillingTemplateEntity::getIsValid, 1);

            if (requestVo.getVehicleType() != null && requestVo.getVehicleType() > 0) {
                templateWrapper.eq(TmsDriverBillingTemplateEntity::getVehicleType, requestVo.getVehicleType());
            }

            List<TmsDriverBillingTemplateEntity> templates = templateService.list(templateWrapper);

            Set<Long> driverIdSet = new HashSet<>(targetDriverIds); // 用于去重

            for (TmsDriverBillingTemplateEntity template : templates) {
                if (StrUtil.isNotBlank(template.getDriverId())) {
                    Arrays.stream(template.getDriverId().trim().split(","))
                            .map(String::trim)
                            .filter(StrUtil::isNotBlank)
                            .map(Long::parseLong) //转为Long
                            .forEach(driverIdSet::add);
                }
            }
            targetDriverIds = new ArrayList<>(driverIdSet);
        }

        return targetDriverIds;
    }

    /**
     * 计算单个司机的费用
     */
    private TmsDriverPieceCalculationResultDto calculateSingleDriverFee(Long driverId, TmsDriverPieceCalculationRequestVo requestVo) {
        TmsDriverPieceCalculationResultDto result = new TmsDriverPieceCalculationResultDto();
        result.setDriverId(driverId);
        result.setAttendanceDate(requestVo.getCalculationDate());

        try {
            // 获取司机信息
            TmsLmdDriverEntity driver = lmdDriverService.getById(driverId);
            if (driver == null) {
                result.setStatus("FAILED");
                result.setErrorMessage("司机不存在");
                return result;
            }
            result.setDriverName(driver.getDriverName());

            // 检查是否已存在记录
            if (!requestVo.getForceRecalculate()) {
                LambdaQueryWrapper<TmsDriverDayReceivableEntity> existWrapper = new LambdaQueryWrapper<>();
                existWrapper.eq(TmsDriverDayReceivableEntity::getDriverId, driverId)
                        .eq(TmsDriverDayReceivableEntity::getAttendanceDate, requestVo.getCalculationDate());

                if (this.count(existWrapper) > 0) {
                    result.setStatus("SKIPPED");
                    result.setErrorMessage("记录已存在，跳过计算");
                    return result;
                }
            }

            // 获取出勤时间
            BigDecimal attendanceHours = getDriverAttendanceHours(driverId, requestVo.getCalculationDate());
            // 出勤验证：如果返回null表示无有效出勤记录，跳过该司机的费用计算
            if (attendanceHours == null) {
                result.setStatus("SKIPPED");
                result.setErrorMessage("无有效出勤记录，跳过费用计算");
                log.info("司机{}({})无有效出勤记录，跳过费用计算", driver.getDriverName(), driverId);
                return result;
            }

            result.setAttendanceHours(attendanceHours);

            // 获取司机的计费模板配置
            TmsDriverBillingTemplateEntity template = getDriverBillingTemplate(driverId);
            if (template == null) {
                result.setStatus("FAILED");
                result.setErrorMessage("未找到司机计费模板");
                return result;
            }

            if (!BILLING_MODE_PIECE.equals(template.getBillingMode())) {
                result.setStatus("FAILED");
                result.setErrorMessage("司机不是计件模式");
                return result;
            }

            result.setVehicleType(template.getVehicleType());

            // 获取计件配置
            LambdaQueryWrapper<TmsDriverBillingPieceEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(TmsDriverBillingPieceEntity::getTemplateId, template.getId());
            TmsDriverBillingPieceEntity pieceConfig = pieceService.getOne(wrapper, false);
            if (pieceConfig == null) {
                result.setStatus("FAILED");
                result.setErrorMessage("未找到计件配置");
                return result;
            }

            result.setMinimumWage(pieceConfig.getMinimumWage());

            // 获取运费配置
            LambdaQueryWrapper<TmsDriverBillingPieceFreightEntity> pieceConfigWrapper = new LambdaQueryWrapper<>();
            pieceConfigWrapper.eq(TmsDriverBillingPieceFreightEntity::getPieceId, pieceConfig.getPieceId())
                    .orderByAsc(TmsDriverBillingPieceFreightEntity::getStartWeight);
            List<TmsDriverBillingPieceFreightEntity> freightConfigs = freightService.list(pieceConfigWrapper);
            if (CollUtil.isEmpty(freightConfigs)) {
                result.setStatus("FAILED");
                result.setErrorMessage("未找到运费配置");
                return result;
            }


            // 直接查询司机当日完成派送的订单
            LambdaQueryWrapper<TmsCustomerOrderEntity> orderWrapper = new LambdaQueryWrapper<>();
            orderWrapper.eq(TmsCustomerOrderEntity::getDeliveryDriverId, driverId);
            // 日期范围查询 - 使用finishTime字段筛选当日完成的订单
            LocalDateTime startTime = requestVo.getCalculationDate().atStartOfDay();
            LocalDateTime endTime = requestVo.getCalculationDate().plusDays(1).atStartOfDay();
            orderWrapper.between(TmsCustomerOrderEntity::getFinishTime, startTime, endTime);
            // 只查询主单，排除子单
            orderWrapper.eq(TmsCustomerOrderEntity::getSubFlag, false);
            List<TmsCustomerOrderEntity> deliveryOrders = customerOrderService.list(orderWrapper);

            // 转换为Map
            Map<String, TmsCustomerOrderEntity> masterOrderMap = deliveryOrders.stream()
                    .collect(Collectors.toMap(TmsCustomerOrderEntity::getEntrustedOrderNumber,
                            order -> order,
                            (existing, replacement) -> existing // 如果有重复key，保留第一个
                    ));

            result.setOrderCount(masterOrderMap.size());
            log.info("司机{}当日完成派送的订单数量: {}", driverId, masterOrderMap.size());

            // 计算基本工资（派件费用)
            BigDecimal freightFee = calculateEnhancedFreightFee(masterOrderMap, freightConfigs);
            result.setFreightFee(freightFee);

            // 计算面签费用（暂时为0）
            BigDecimal faceSignFee = BigDecimal.ZERO;
            result.setFaceSignFee(faceSignFee);

            // 计算奖励金（主订单数 × 司机模板计件配置中的奖励金额）
            BigDecimal bonus = calculateEnhancedBonus(masterOrderMap, pieceConfig);
            result.setBonus(bonus);

            // 计算保底前费用（实际派件金额）
            BigDecimal preMinimumTotal = freightFee.add(faceSignFee).add(bonus);
            result.setPreMinimumTotal(preMinimumTotal);

            // 计算保底后费用
            BigDecimal postMinimumTotal = preMinimumTotal.max(pieceConfig.getMinimumWage());
            result.setPostMinimumTotal(postMinimumTotal);

            // 保存记录
            TmsDriverDayReceivableEntity entity = new TmsDriverDayReceivableEntity();
            BeanUtils.copyProperties(result,entity);
            entity.setRemark("系统自动计算生成");
            boolean saveResult = this.save(entity);

            if (saveResult) {
                result.setRecordId(entity.getId());
                result.setStatus("SUCCESS");
            } else {
                result.setStatus("FAILED");
                result.setErrorMessage("保存记录失败");
            }

        } catch (Exception e) {
            log.error("计算司机{}费用异常", driverId, e);
            result.setStatus("FAILED");
            result.setErrorMessage("计算异常：" + e.getMessage());
            throw new RuntimeException("计算异常：" + e.getMessage());
        }

        return result;
    }

    /**
     * 获取司机的计费模板
     */
    private TmsDriverBillingTemplateEntity getDriverBillingTemplate(Long driverId) {
        LambdaQueryWrapper<TmsDriverBillingTemplateEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsDriverBillingTemplateEntity::getBillingMode, BILLING_MODE_PIECE)
                .eq(TmsDriverBillingTemplateEntity::getIsValid, 1);

        List<TmsDriverBillingTemplateEntity> templates = templateService.list(wrapper);

        for (TmsDriverBillingTemplateEntity template : templates) {
            if (StrUtil.isNotBlank(template.getDriverId())) {
                String[] driverIdArray = template.getDriverId().split(",");
                for (String driverIdStr : driverIdArray) {
                    if (StrUtil.isNotBlank(driverIdStr.trim())) {
                        try {
                            Long templateDriverId = Long.parseLong(driverIdStr.trim());
                            if (driverId.equals(templateDriverId)) {
                                return template;
                            }
                        } catch (NumberFormatException e) {
                            log.warn("解析司机ID失败：{}", driverIdStr);
                        }
                    }
                }
            }
        }

        return null;
    }



    /**
     * 获取司机出勤时间
     */
    private BigDecimal getDriverAttendanceHours(Long driverId, LocalDate date) {
        // 先获取司机信息获取司机号
        TmsLmdDriverEntity driver = lmdDriverService.getById(driverId);
        if (driver == null || StrUtil.isBlank(driver.getDriverNum())) {
            log.warn("司机{}信息不存在或司机号为空，跳过费用计算", driverId);
            return null; // 返回null表示跳过计算
        }

        // 查询司机签到记录
        LambdaQueryWrapper<TmsDriverSignEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TmsDriverSignEntity::getDriverNum, driver.getDriverNum())
                .eq(TmsDriverSignEntity::getAttendanceDate, date);

        TmsDriverSignEntity signRecord = driverSignService.getOne(wrapper, false);

        // 验证出勤记录
        if (signRecord == null) {
            log.info("司机{}({})在{}没有出勤记录，跳过费用计算", driver.getDriverName(), driverId, date);
            return null; // 返回null表示跳过计算
        }

        // 验证出勤小时数
        if (signRecord.getAttendanceHours() == null || signRecord.getAttendanceHours().compareTo(BigDecimal.ZERO) <= 0) {
            log.info("司机{}({})在{}的出勤小时数无效({}小时)，跳过费用计算", driver.getDriverName(), driverId, date, signRecord.getAttendanceHours());
            return null; // 返回null表示跳过计算
        }

        log.debug("司机{}({})在{}的出勤时间：{}小时", driver.getDriverName(), driverId, date, signRecord.getAttendanceHours());
        return signRecord.getAttendanceHours();
    }



    /**
     * 根据重量获取对应的运费配置对象（首件+续件）
     */
    private TmsDriverBillingPieceFreightEntity getFreightConfigByWeight(BigDecimal weight, List<TmsDriverBillingPieceFreightEntity> freightConfigs) {
        for (TmsDriverBillingPieceFreightEntity config : freightConfigs) {
            if (weight.compareTo(config.getStartWeight()) >= 0 &&
                    (config.getEndWeight() == null || weight.compareTo(config.getEndWeight()) <= 0)) {
                return config;
            }
        }
        return null;
    }



    /**
     * 费用计算方法
     *
     */
    private BigDecimal calculateEnhancedFreightFee(Map<String, TmsCustomerOrderEntity> masterOrderMap, List<TmsDriverBillingPieceFreightEntity> freightConfigs) {
        BigDecimal totalFee = BigDecimal.ZERO;
        try {
            // 按主订单分组处理
            for (Map.Entry<String, TmsCustomerOrderEntity> entry : masterOrderMap.entrySet()) {
                String masterOrderNo = entry.getKey();
                TmsCustomerOrderEntity masterOrder = entry.getValue();

                log.debug("处理主订单：{}", masterOrderNo);

                // 获取该主订单的所有子订单
                LambdaQueryWrapper<TmsCustomerOrderEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.likeRight(TmsCustomerOrderEntity::getEntrustedOrderNumber, masterOrderNo)
                        .eq(TmsCustomerOrderEntity::getSubFlag, true) // 只查询子订单
                        .eq(TmsCustomerOrderEntity::getDelFlag, "0")
                        .orderByAsc(TmsCustomerOrderEntity::getEntrustedOrderNumber);

                List<TmsCustomerOrderEntity> subOrders = customerOrderService.list(wrapper);
                log.debug("主订单{}找到{}个子订单", masterOrderNo, subOrders.size());

                if (CollUtil.isNotEmpty(subOrders))  {
                    // 按件计算
                    BigDecimal masterOrderFee = calculateMultiPieceOrderFee(masterOrder, subOrders, freightConfigs);
                    totalFee = totalFee.add(masterOrderFee);
                    log.debug("主订单{}有{}个子订单，总费用：{}", masterOrderNo, subOrders.size(), masterOrderFee);
                    return totalFee;
                }

                return totalFee;
            }
        } catch (Exception e) {
            log.error("费用计算异常", e);
        }

        return totalFee.setScale(2, RoundingMode.HALF_UP);
    }


    /**
     * 计算一票多件订单的费用
     * 首件：选择重量最重的子件按首件单价计算（重量 × 首件单价）
     * 续件：其余所有子件按续件单价计算（重量 × 续件单价）
     */
    private BigDecimal calculateMultiPieceOrderFee(TmsCustomerOrderEntity masterOrder, List<TmsCustomerOrderEntity> subOrders,
                                                 List<TmsDriverBillingPieceFreightEntity> freightConfigs) {
        BigDecimal totalFee = BigDecimal.ZERO;

        // 找出重量最重的子订单作为首件
        TmsCustomerOrderEntity heaviestSubOrder = findHeaviestSubOrder(subOrders);
        BigDecimal heaviestWeight = getOrderWeight(heaviestSubOrder);


        // 获取最重子单对应的运费配置
        TmsDriverBillingPieceFreightEntity matchedFreightConfig = getFreightConfigByWeight(heaviestWeight, freightConfigs);
        if (matchedFreightConfig == null) {
            log.warn("未匹配到运费配置，单号：{}", masterOrder.getEntrustedOrderNumber());
            return BigDecimal.ZERO;
        }
        // 首件费用
        BigDecimal firstUnitPrice = matchedFreightConfig.getFirstItemPrice();
        BigDecimal firstFee = heaviestWeight.multiply(firstUnitPrice);
        totalFee = totalFee.add(firstFee);

        log.debug("首件订单{}（重量最重），重量：{}kg，单价：{}，费用：{}", heaviestSubOrder.getEntrustedOrderNumber(), heaviestWeight, firstUnitPrice, firstFee);

        // 续件费用，使用首件同一配置的续件单价
        BigDecimal additionalUnitPrice = matchedFreightConfig.getAdditionalItemPrice();

        // 其余所有子件按续件单价计算
        for (TmsCustomerOrderEntity subOrder : subOrders) {
            // 跳过已经作为首件计算的最重子订单
            if (subOrder.getId().equals(heaviestSubOrder.getId())) {
                continue;
            }
            BigDecimal weight = getOrderWeight(subOrder);
            BigDecimal additionalFee = weight.multiply(additionalUnitPrice);
            totalFee = totalFee.add(additionalFee);
            log.debug("续件订单{}，重量：{}kg，续件单价：{}，费用：{}", subOrder.getEntrustedOrderNumber(), weight, additionalUnitPrice, additionalFee);
        }
        log.debug("一票多件订单{}，总件数：{}，总费用：{}", masterOrder.getEntrustedOrderNumber(), subOrders.size(), totalFee);
        return totalFee;
    }

    /**
     * 从子订单列表中找出重量最重的订单
     * 如果重量相同，则选择第一个遇到的订单
     */
    private TmsCustomerOrderEntity findHeaviestSubOrder(List<TmsCustomerOrderEntity> subOrders) {
        if (CollUtil.isEmpty(subOrders)) {
            return null;
        }

        TmsCustomerOrderEntity heaviestOrder = subOrders.get(0);
        BigDecimal maxWeight = getOrderWeight(heaviestOrder);

        for (TmsCustomerOrderEntity subOrder : subOrders) {
            BigDecimal currentWeight = getOrderWeight(subOrder);
            if (currentWeight.compareTo(maxWeight) > 0) {
                maxWeight = currentWeight;
                heaviestOrder = subOrder;
            }
        }

        log.debug("从{}个子订单中选择重量最重的订单：{}，重量：{}kg", subOrders.size(), heaviestOrder.getEntrustedOrderNumber(), maxWeight);
        return heaviestOrder;
    }

    /**
     * 获取订单的重量，处理空值情况
     * 如果重量为空或为0，则默认为1kg
     */
    private BigDecimal getOrderWeight(TmsCustomerOrderEntity order) {
        if (order == null || order.getTotalWeight() == null ||
            BigDecimal.ZERO.compareTo(order.getTotalWeight()) == 0) {
            return BigDecimal.ONE;
        }
        return order.getTotalWeight();
    }

    /**
     * 奖金计算方法
     * 基于实际的主订单数量和司机模板计件配置中的奖励金额计算奖金（主订单数 × 司机模板奖励金额）
     */
    private BigDecimal calculateEnhancedBonus(Map<String, TmsCustomerOrderEntity> masterOrderMap, TmsDriverBillingPieceEntity pieceConfig) {
        if (CollUtil.isEmpty(masterOrderMap)) {
            return BigDecimal.ZERO;
        }

        // 统计主订单数量
        int masterOrderCount = masterOrderMap.size();

        // 默认奖励金额
        BigDecimal defaultBonusAmount = new BigDecimal("0.2");
        // 最终计算奖励金金额
        BigDecimal bonusAmount = BigDecimal.ZERO;
        if (pieceConfig == null) {
            log.warn("计件配置为空，使用默认奖励金额：{}", defaultBonusAmount);
            bonusAmount = defaultBonusAmount;
        } else {
            bonusAmount = pieceConfig.getBonus();
        }
        // 计算总奖励金：主订单数 × 奖励金额
        BigDecimal bonus = new BigDecimal(masterOrderCount).multiply(bonusAmount);
        log.debug("主订单数量：{}，单笔奖励金额：{}，总奖金：{}", masterOrderCount, bonusAmount, bonus);
        return bonus.setScale(2, RoundingMode.HALF_UP);
    }

}